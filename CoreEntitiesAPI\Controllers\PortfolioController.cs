﻿using Microsoft.AspNetCore.Mvc;
using CoreEntitiesAPI.Models;
using CoreEntitiesAPI.Services.Interfaces;
using CoreEntitiesAPI.DTOs;

[ApiController]
[Route("api/[controller]")]
public class PortfolioController : ControllerBase
{
    private readonly IPortfolioService _service;

    public PortfolioController(IPortfolioService service)
    {
        _service = service;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll() =>
        Ok(await _service.GetAllAsync());

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        var portfolio = await _service.GetByIdAsync(id);
        return portfolio == null ? NotFound() : Ok(portfolio);
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] PortfolioDto dto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var created = await _service.AddAsync(dto);
        return CreatedAtAction(nameof(Get), new { id = created.Id }, created);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] PortfolioDto dto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var updated = await _service.UpdateAsync(id, dto);
        if (updated == null)
            return NotFound();

        return Ok(updated);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        var result = await _service.DeleteAsync(id);
        return result ? Ok() : BadRequest("Cannot delete. Ensure no child projects remain.");
    }
}
