﻿using Microsoft.EntityFrameworkCore;
using CoreEntitiesAPI.Data;
using CoreEntitiesAPI.Models;
using CoreEntitiesAPI.DTOs;
using CoreEntitiesAPI.Services.Interfaces;

namespace CoreEntitiesAPI.Services.Implementations
{
    public class ProjectService : IProjectService
    {
        private readonly ApplicationDbContext _context;

        public ProjectService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Project>> GetAllAsync()
        {
            return await _context.Projects.Include(p => p.Portfolio).ToListAsync();
        }

        public async Task<Project?> GetByIdAsync(int id)
        {
            return await _context.Projects.Include(p => p.Portfolio)
                                          .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Project> AddAsync(ProjectDto dto)
        {
            var project = new Project
            {
                Name = dto.Name,
                Description = dto.Description,
                PortfolioId = dto.PortfolioId
            };

            _context.Projects.Add(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task<Project?> UpdateAsync(int id, ProjectDto dto)
        {
            var project = await _context.Projects.FindAsync(id);
            if (project == null)
                return null;

            project.Name = dto.Name;
            project.Description = dto.Description;
            project.PortfolioId = dto.PortfolioId;

            _context.Projects.Update(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var project = await _context.Projects.FindAsync(id);
            if (project == null)
                return false;

            _context.Projects.Remove(project);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
