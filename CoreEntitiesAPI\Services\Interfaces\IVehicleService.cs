﻿using System;
using Microsoft.EntityFrameworkCore;
using CoreEntitiesAPI.Data;
using CoreEntitiesAPI.Repositories.Interfaces;
using CoreEntitiesAPI.Repositories.Implementations;
using CoreEntitiesAPI.Services.Interfaces;
using CoreEntitiesAPI.Services.Implementations;
using CoreEntitiesAPI.DTOs;
using CoreEntitiesAPI.Models;

namespace CoreEntitiesAPI.Services.Interfaces
{
    public interface IVehicleService
    {
        Task<IEnumerable<Vehicle>> GetAllAsync();
        Task<Vehicle?> GetByIdAsync(int id);
        Task AddAsync(Vehicle vehicle);
        Task<bool> DeleteAsync(int id);
        Task UpdateAsync(Vehicle vehicle);
    }
}