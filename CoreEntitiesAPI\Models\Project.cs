﻿using System;
using System.Text.Json.Serialization;

namespace CoreEntitiesAPI.Models
{
    public class Project
    {
        public int Id { get; set; }
        public string? Description { get; set; }
        public int PortfolioId { get; set; }
        public string Name { get; set; } = null!;
        [JsonIgnore] // <- prevent circular serialization
        public virtual Portfolio Portfolio { get; set; } = null!;
    }
}