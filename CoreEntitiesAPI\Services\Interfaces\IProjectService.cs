﻿using System;
using Microsoft.EntityFrameworkCore;
using CoreEntitiesAPI.Data;
using CoreEntitiesAPI.Repositories.Interfaces;
using CoreEntitiesAPI.Repositories.Implementations;
using CoreEntitiesAPI.Services.Interfaces;
using CoreEntitiesAPI.Services.Implementations;
using CoreEntitiesAPI.Models;
using CoreEntitiesAPI.DTOs;

namespace CoreEntitiesAPI.Services.Interfaces
{
    public interface IProjectService
    {
        Task<IEnumerable<Project>> GetAllAsync();
        Task<Project?> GetByIdAsync(int id);
        Task<Project> AddAsync(ProjectDto dto);
        Task<Project?> UpdateAsync(int id, ProjectDto dto);
        Task<bool> DeleteAsync(int id);
    }
}
