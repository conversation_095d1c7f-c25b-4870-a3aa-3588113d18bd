﻿using Microsoft.EntityFrameworkCore;
using CoreEntitiesAPI.Data;
using CoreEntitiesAPI.DTOs;
using CoreEntitiesAPI.Models;
using CoreEntitiesAPI.Repositories.Interfaces;
using CoreEntitiesAPI.Repositories.Implementations;
using CoreEntitiesAPI.Services.Interfaces;
using CoreEntitiesAPI.Services.Implementations;

namespace CoreEntitiesAPI.Services.Implementations
{
    public class PortfolioService : IPortfolioService
    {
        private readonly ApplicationDbContext _context;

        public PortfolioService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Portfolio>> GetAllAsync()
        {
            return await _context.Portfolios
                .Include(p => p.Projects)
                .ToListAsync();
        }

        public async Task<Portfolio?> GetByIdAsync(int id)
        {
            return await _context.Portfolios
                .Include(p => p.Projects)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Portfolio> AddAsync(PortfolioDto dto)
        {
            var portfolio = new Portfolio
            {
                Name = dto.Name,
                Description = dto.Description
            };

            _context.Portfolios.Add(portfolio);
            await _context.SaveChangesAsync();
            return portfolio;
        }

        public async Task<Portfolio?> UpdateAsync(int id, PortfolioDto dto)
        {
            var portfolio = await _context.Portfolios.FindAsync(id);
            if (portfolio == null)
                return null;

            portfolio.Name = dto.Name;
            portfolio.Description = dto.Description;

            _context.Portfolios.Update(portfolio);
            await _context.SaveChangesAsync();

            return portfolio;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var portfolio = await _context.Portfolios
                .Include(p => p.Projects)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (portfolio == null)
                return false;

            if (portfolio.Projects.Any())
                _context.Projects.RemoveRange(portfolio.Projects);

            _context.Portfolios.Remove(portfolio);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
