﻿using System;
using Microsoft.EntityFrameworkCore;
using CoreEntitiesAPI.Data;
using CoreEntitiesAPI.Repositories.Interfaces;
using CoreEntitiesAPI.Repositories.Implementations;
using CoreEntitiesAPI.Services.Interfaces;
using CoreEntitiesAPI.Services.Implementations;
using CoreEntitiesAPI.DTOs;
using CoreEntitiesAPI.Models;

namespace CoreEntitiesAPI.Services.Interfaces
{
    public interface IPortfolioService
    {
        Task<IEnumerable<Portfolio>> GetAllAsync();
        Task<Portfolio?> GetByIdAsync(int id);
        Task<Portfolio> AddAsync(PortfolioDto dto);
        Task<Portfolio?> UpdateAsync(int id, PortfolioDto dto);
        Task<bool> DeleteAsync(int id);
    }
}
